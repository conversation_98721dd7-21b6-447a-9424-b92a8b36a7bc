from app.models.user import UserResponse, UserCreate, UserUpdate, UserTier
from app.models.content import ContentType, GenerationStatus
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime
import uuid

class MockDatabaseManager:
    """Mock database operations manager for testing without Supabase"""
    
    def __init__(self):
        # In-memory storage for testing
        self.users = {}
        self.content = {}
        self.usage_logs = []
    
    # User operations
    async def get_user_by_id(self, user_id: str) -> Optional[UserResponse]:
        """Get user by ID"""
        try:
            if user_id in self.users:
                user_data = self.users[user_id]
                return UserResponse(**user_data)
            return None
        except Exception as e:
            logging.error(f"Error getting user by ID {user_id}: {str(e)}")
            return None
    
    async def get_user_by_email(self, email: str) -> Optional[UserResponse]:
        """Get user by email"""
        try:
            for user_data in self.users.values():
                if user_data['email'] == email:
                    return UserResponse(**user_data)
            return None
        except Exception as e:
            logging.error(f"Error getting user by email {email}: {str(e)}")
            return None
    
    async def create_user(self, user_data: UserCreate) -> Optional[UserResponse]:
        """Create new user"""
        try:
            user_id = str(uuid.uuid4())
            new_user = {
                'id': user_id,
                'email': user_data.email,
                'name': user_data.name,
                'avatar_url': user_data.avatar_url,
                'tier': user_data.tier,
                'gemini_api_key': None,
                'usage_count': 0,
                'monthly_usage_limit': 100,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow(),
                'is_active': True,
                'has_api_key': False
            }
            self.users[user_id] = new_user
            return UserResponse(**new_user)
        except Exception as e:
            logging.error(f"Error creating user: {str(e)}")
            return None
    
    async def update_user(self, user_id: str, update_data: UserUpdate) -> Optional[UserResponse]:
        """Update user information"""
        try:
            if user_id not in self.users:
                return None
            
            user_data = self.users[user_id].copy()
            update_dict = {k: v for k, v in update_data.model_dump().items() if v is not None}
            
            for key, value in update_dict.items():
                if key in user_data:
                    user_data[key] = value
            
            user_data['updated_at'] = datetime.utcnow()
            user_data['has_api_key'] = bool(user_data.get('gemini_api_key'))
            
            self.users[user_id] = user_data
            return UserResponse(**user_data)
        except Exception as e:
            logging.error(f"Error updating user {user_id}: {str(e)}")
            return None
    
    async def increment_usage(self, user_id: str) -> bool:
        """Increment user's usage count"""
        try:
            if user_id in self.users:
                self.users[user_id]['usage_count'] += 1
                return True
            return False
        except Exception as e:
            logging.error(f"Error incrementing usage for user {user_id}: {str(e)}")
            return False
    
    # Content operations
    async def create_generated_content(
        self, 
        user_id: str, 
        content_type: ContentType, 
        input_data: Dict[str, Any],
        output_data: Optional[Dict[str, Any]] = None,
        status: GenerationStatus = GenerationStatus.PENDING
    ) -> Optional[str]:
        """Create new generated content record"""
        try:
            content_id = str(uuid.uuid4())
            content_data = {
                'id': content_id,
                'user_id': user_id,
                'content_type': content_type.value,
                'input_data': input_data,
                'output_data': output_data,
                'status': status.value,
                'error_message': None,
                'generation_time_ms': None,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            }
            
            self.content[content_id] = content_data
            return content_id
        except Exception as e:
            logging.error(f"Error creating generated content: {str(e)}")
            return None
    
    async def update_generated_content(
        self, 
        content_id: str, 
        output_data: Dict[str, Any], 
        status: GenerationStatus,
        generation_time_ms: Optional[int] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """Update generated content with results"""
        try:
            if content_id not in self.content:
                return False
            
            self.content[content_id]['output_data'] = output_data
            self.content[content_id]['status'] = status.value
            self.content[content_id]['updated_at'] = datetime.utcnow()
            
            if generation_time_ms is not None:
                self.content[content_id]['generation_time_ms'] = generation_time_ms
            
            if error_message is not None:
                self.content[content_id]['error_message'] = error_message
            
            return True
        except Exception as e:
            logging.error(f"Error updating generated content {content_id}: {str(e)}")
            return False
    
    async def get_generated_content(self, content_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get generated content by ID"""
        try:
            if content_id in self.content:
                content_data = self.content[content_id]
                if content_data['user_id'] == user_id:
                    return content_data
            return None
        except Exception as e:
            logging.error(f"Error getting generated content {content_id}: {str(e)}")
            return None
    
    async def get_user_content_history(
        self, 
        user_id: str, 
        content_type: Optional[ContentType] = None,
        limit: int = 20,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get user's content generation history"""
        try:
            user_content = []
            for content_data in self.content.values():
                if content_data['user_id'] == user_id:
                    if content_type is None or content_data['content_type'] == content_type.value:
                        user_content.append(content_data)
            
            # Sort by created_at descending
            user_content.sort(key=lambda x: x['created_at'], reverse=True)
            
            # Apply pagination
            return user_content[offset:offset + limit]
        except Exception as e:
            logging.error(f"Error getting content history for user {user_id}: {str(e)}")
            return []
    
    # Usage logging
    async def log_usage(
        self, 
        user_id: str, 
        content_type: ContentType, 
        api_key_used: str,
        tokens_used: int = 0,
        cost_usd: float = 0.0
    ) -> bool:
        """Log API usage"""
        try:
            usage_data = {
                'id': str(uuid.uuid4()),
                'user_id': user_id,
                'content_type': content_type.value,
                'api_key_used': api_key_used,
                'tokens_used': tokens_used,
                'cost_usd': cost_usd,
                'created_at': datetime.utcnow()
            }
            
            self.usage_logs.append(usage_data)
            return True
        except Exception as e:
            logging.error(f"Error logging usage: {str(e)}")
            return False

# Create a test user for development
def create_test_user():
    """Create a test user for development"""
    mock_db = MockDatabaseManager()
    test_user_data = {
        'id': 'test-user-123',
        'email': '<EMAIL>',
        'name': 'Test User',
        'avatar_url': 'https://example.com/avatar.jpg',
        'tier': UserTier.FREE,
        'gemini_api_key': None,
        'usage_count': 5,
        'monthly_usage_limit': 100,
        'created_at': datetime.utcnow(),
        'updated_at': datetime.utcnow(),
        'is_active': True,
        'has_api_key': False
    }
    mock_db.users['test-user-123'] = test_user_data
    return mock_db

# Global mock database manager instance
mock_db_manager = create_test_user()
