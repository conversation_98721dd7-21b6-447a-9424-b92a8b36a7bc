# Supabase Setup Guide

This guide will help you set up Supabase for the Content Creator Agents Platform.

## Prerequisites

1. Create a Supabase account at [supabase.com](https://supabase.com)
2. Create a new project in your Supabase dashboard

## Step 1: Get Your Supabase Credentials

1. Go to your Supabase project dashboard
2. Navigate to **Settings** → **API**
3. Copy the following values:
   - **Project URL** (e.g., `https://your-project-id.supabase.co`)
   - **Anon public key** (starts with `eyJ...`)
   - **Service role key** (starts with `eyJ...`) - **Keep this secret!**

## Step 2: Update Environment Variables

1. Open the `.env` file in your project root
2. Replace the placeholder values with your actual Supabase credentials:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your_anon_public_key_here
SUPABASE_SERVICE_KEY=your_service_role_key_here
```

## Step 3: Set Up Database Schema

1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the contents of `database_schema.sql` into the SQL editor
4. Click **Run** to execute the schema

This will create:
- User tables with proper authentication integration
- Content generation tables
- Usage tracking tables
- Row Level Security (RLS) policies
- Triggers for automatic user creation

## Step 4: Configure Authentication

1. In your Supabase dashboard, go to **Authentication** → **Settings**
2. Under **Auth Providers**, enable **Google** if you want Google sign-in
3. Configure the OAuth settings with your Google OAuth credentials

## Step 5: Test the Integration

1. Update your code to use the real Supabase database instead of the mock:

In `app/api/auth.py`, change:
```python
from app.utils.mock_database import mock_db_manager as db_manager
```

To:
```python
from app.utils.database import db_manager
```

2. Restart your FastAPI server
3. Test the endpoints to ensure they work with the real database

## Step 6: Security Considerations

1. **Never commit your `.env` file** to version control
2. **Keep your service role key secret** - it has admin privileges
3. **Use the anon key for client-side operations** only
4. **Review and test your RLS policies** before going to production

## Troubleshooting

### Connection Issues
- Verify your SUPABASE_URL and SUPABASE_KEY are correct
- Check that your project is not paused (free tier projects pause after inactivity)
- Ensure your IP is not blocked by any firewall rules

### Authentication Issues
- Verify your JWT secret matches between your app and Supabase
- Check that RLS policies are properly configured
- Ensure users table is properly linked to auth.users

### Database Issues
- Check the SQL editor for any schema creation errors
- Verify all tables were created successfully
- Test database connections using the Supabase dashboard

## Next Steps

Once Supabase is set up:
1. Implement proper JWT token validation
2. Add user registration/login flows
3. Test the complete authentication workflow
4. Set up proper error handling and logging
5. Configure production environment variables

## Support

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Discord Community](https://discord.supabase.com)
- [FastAPI + Supabase Integration Guide](https://supabase.com/docs/guides/getting-started/tutorials/with-fastapi)
