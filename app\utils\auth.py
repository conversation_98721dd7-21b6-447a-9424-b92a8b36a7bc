"""
Authentication utilities for JWT token validation with Supabase
"""
import jwt
import logging
from typing import Optional, Dict, Any
from fastapi import HTTPException, status
from app.config import settings
from app.database import get_supabase_client
import httpx
import json

class SupabaseAuth:
    """Supabase authentication handler"""
    
    def __init__(self):
        self.supabase_url = settings.supabase_url
        self.jwt_secret = None
        self._jwt_secret_fetched = False
    
    async def _get_jwt_secret(self) -> str:
        """Get JWT secret from Supabase"""
        if self.jwt_secret and self._jwt_secret_fetched:
            return self.jwt_secret
        
        try:
            # For Supabase, the JWT secret is typically the same as the service key
            # or can be found in the project settings
            self.jwt_secret = settings.supabase_service_key
            self._jwt_secret_fetched = True
            return self.jwt_secret
        except Exception as e:
            logging.error(f"Error getting JWT secret: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication configuration error"
            )
    
    async def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Verify JWT token and return user information
        """
        try:
            # For Supabase JWT tokens, we can verify them using the service key
            # The token contains user information and is signed by Supabase
            
            # First, try to decode without verification to check structure
            unverified_payload = jwt.decode(token, options={"verify_signature": False})
            
            # Check if it's a Supabase token
            if unverified_payload.get('iss') != 'supabase':
                logging.warning("Token is not from Supabase")
                return None
            
            # Get the JWT secret
            secret = await self._get_jwt_secret()
            
            # Verify the token
            payload = jwt.decode(
                token,
                secret,
                algorithms=["HS256"],
                audience="authenticated"
            )
            
            # Extract user information
            user_id = payload.get('sub')
            email = payload.get('email')
            role = payload.get('role', 'authenticated')
            
            if not user_id:
                logging.warning("Token does not contain user ID")
                return None
            
            return {
                'user_id': user_id,
                'email': email,
                'role': role,
                'payload': payload
            }
            
        except jwt.ExpiredSignatureError:
            logging.warning("Token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logging.warning(f"Invalid token: {str(e)}")
            return None
        except Exception as e:
            logging.error(f"Error verifying token: {str(e)}")
            return None
    
    async def get_user_from_supabase(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get user information from Supabase using the user ID
        """
        try:
            client = get_supabase_client()
            response = client.auth.admin.get_user_by_id(user_id)
            
            if response.user:
                return {
                    'id': response.user.id,
                    'email': response.user.email,
                    'created_at': response.user.created_at,
                    'last_sign_in_at': response.user.last_sign_in_at,
                    'user_metadata': response.user.user_metadata
                }
            return None
        except Exception as e:
            logging.error(f"Error getting user from Supabase: {str(e)}")
            return None

# Global auth instance
supabase_auth = SupabaseAuth()

async def get_current_user_from_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Get current user information from JWT token
    """
    if not token:
        return None
    
    # Verify the token
    token_data = await supabase_auth.verify_token(token)
    if not token_data:
        return None
    
    return token_data

async def require_authentication(token: Optional[str]) -> Dict[str, Any]:
    """
    Require valid authentication and return user data
    """
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    user_data = await get_current_user_from_token(token)
    if not user_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    return user_data

def extract_token_from_header(authorization: Optional[str]) -> Optional[str]:
    """
    Extract JWT token from Authorization header
    """
    if not authorization:
        return None
    
    if not authorization.startswith("Bearer "):
        return None
    
    return authorization[7:]  # Remove "Bearer " prefix

# Backward compatibility function for simple user ID extraction
def extract_user_id_simple(authorization: Optional[str]) -> Optional[str]:
    """
    Simple user ID extraction for backward compatibility
    This is used when we want to support both JWT tokens and simple user IDs
    """
    if not authorization:
        return None
    
    if authorization.startswith("Bearer "):
        token = authorization[7:]
        
        # If it looks like a JWT token (contains dots), try to decode it
        if '.' in token:
            try:
                # Try to decode as JWT
                unverified_payload = jwt.decode(token, options={"verify_signature": False})
                return unverified_payload.get('sub')
            except:
                # If JWT decode fails, treat as simple user ID
                return token
        else:
            # Simple user ID
            return token
    
    return None
