from pydantic_settings import BaseSettings
from typing import Optional
import os
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseSettings):
    # App settings
    app_name: str = "Content Creator Agents Platform"
    debug: bool = True

    # Supabase settings
    supabase_url: str = ""
    supabase_key: str = ""
    supabase_service_key: str = ""

    # Database settings
    database_url: str = ""

    # Gemini API settings (for premium users)
    gemini_api_key: str = ""

    # JWT settings
    secret_key: str = "your-secret-key-change-this"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30

    # Rate limiting
    rate_limit_per_minute: int = 60
    premium_rate_limit_per_minute: int = 300

    class Config:
        env_file = ".env"

settings = Settings()
