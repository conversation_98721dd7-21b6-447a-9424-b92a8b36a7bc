#!/usr/bin/env python3
"""
Complete workflow test for the Content Creator Agents Platform
"""
import requests
import json
import time

BASE_URL = "http://localhost:8000"
API_KEY = "AIzaSyAaJTHPJgKhZkuKhc-EVrFW6-AfaCzuh_g"

def test_health_checks():
    """Test all health check endpoints"""
    print("🔍 Testing health checks...")
    
    # Main health check
    response = requests.get(f"{BASE_URL}/health")
    assert response.status_code == 200
    print("✅ Main health check passed")
    
    # Auth health check
    response = requests.get(f"{BASE_URL}/api/auth/health")
    assert response.status_code == 200
    print("✅ Auth health check passed")
    
    # Agents health check
    response = requests.get(f"{BASE_URL}/api/agents/health")
    assert response.status_code == 200
    print("✅ Agents health check passed")

def test_api_key_validation():
    """Test Gemini API key validation"""
    print("\n🔑 Testing API key validation...")
    
    # Test valid API key
    response = requests.post(f"{BASE_URL}/api/auth/validate-api-key", json={
        "api_key": API_KEY
    })
    assert response.status_code == 200
    data = response.json()
    assert data["is_valid"] == True
    print("✅ Valid API key validation passed")
    
    # Test invalid API key
    response = requests.post(f"{BASE_URL}/api/auth/validate-api-key", json={
        "api_key": "invalid_key"
    })
    assert response.status_code == 200
    data = response.json()
    assert data["is_valid"] == False
    print("✅ Invalid API key validation passed")

def test_title_tags_generation():
    """Test title and tags generation"""
    print("\n🎯 Testing title and tags generation...")
    
    # Test with user API key
    response = requests.post(f"{BASE_URL}/api/agents/generate-titles-tags", json={
        "script": "In this comprehensive tutorial, I'll walk you through 10 proven productivity techniques that have helped thousands of professionals boost their efficiency and achieve better work-life balance.",
        "topic": "Productivity and Time Management",
        "keywords": ["productivity", "time management", "efficiency", "work-life balance", "professional development"],
        "target_audience": "working professionals and entrepreneurs",
        "user_api_key": API_KEY
    })
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify response structure
    assert "titles" in data
    assert "tags" in data
    assert "seo_score" in data
    assert isinstance(data["titles"], list)
    assert isinstance(data["tags"], list)
    assert len(data["titles"]) > 0
    assert len(data["tags"]) > 0
    
    print(f"✅ Generated {len(data['titles'])} titles and {len(data['tags'])} tags")
    print(f"📊 SEO Score: {data['seo_score']}")
    
    # Display some results
    print("\n📝 Sample Titles:")
    for i, title in enumerate(data["titles"][:3], 1):
        print(f"   {i}. {title}")
    
    print("\n🏷️ Sample Tags:")
    print(f"   {', '.join(data['tags'][:5])}")
    
    return data

def test_generate_more_functionality(original_data):
    """Test generate more titles and tags"""
    print("\n🔄 Testing generate more functionality...")
    
    response = requests.post(f"{BASE_URL}/api/agents/generate-more-titles-tags", json={
        "original_request": {
            "script": "In this comprehensive tutorial, I'll walk you through 10 proven productivity techniques.",
            "topic": "Productivity and Time Management",
            "keywords": ["productivity", "time management", "efficiency"],
            "target_audience": "working professionals",
            "user_api_key": API_KEY
        },
        "exclude_titles": original_data["titles"][:2],  # Exclude first 2 titles
        "exclude_tags": original_data["tags"][:3]       # Exclude first 3 tags
    })
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify response structure
    assert "titles" in data
    assert "tags" in data
    assert len(data["titles"]) > 0
    assert len(data["tags"]) > 0
    
    print(f"✅ Generated {len(data['titles'])} additional titles and {len(data['tags'])} additional tags")
    
    # Display some results
    print("\n📝 Additional Titles:")
    for i, title in enumerate(data["titles"][:3], 1):
        print(f"   {i}. {title}")

def test_authentication_endpoints():
    """Test authentication-related endpoints"""
    print("\n🔐 Testing authentication endpoints...")
    
    # Test token verification with invalid token
    response = requests.get(f"{BASE_URL}/api/auth/verify-token", headers={
        "Authorization": "Bearer invalid_token"
    })
    assert response.status_code == 200
    data = response.json()
    assert data["valid"] == False
    print("✅ Invalid token verification passed")
    
    # Test without authorization header
    response = requests.get(f"{BASE_URL}/api/auth/verify-token")
    assert response.status_code == 401
    print("✅ Missing authorization header test passed")

def test_error_handling():
    """Test error handling scenarios"""
    print("\n⚠️ Testing error handling...")
    
    # Test title generation without API key
    response = requests.post(f"{BASE_URL}/api/agents/generate-titles-tags", json={
        "script": "Test script",
        "topic": "Test Topic",
        "keywords": ["test"],
        "target_audience": "test audience"
        # No user_api_key provided
    })
    assert response.status_code == 400
    print("✅ Missing API key error handling passed")
    
    # Test with invalid JSON
    response = requests.post(f"{BASE_URL}/api/agents/generate-titles-tags", 
                           data="invalid json",
                           headers={"Content-Type": "application/json"})
    assert response.status_code == 422
    print("✅ Invalid JSON error handling passed")

def test_performance():
    """Test basic performance metrics"""
    print("\n⚡ Testing performance...")
    
    start_time = time.time()
    
    response = requests.post(f"{BASE_URL}/api/agents/generate-titles-tags", json={
        "script": "Quick performance test script about productivity tips for busy professionals.",
        "topic": "Productivity",
        "keywords": ["productivity", "tips"],
        "target_audience": "professionals",
        "user_api_key": API_KEY
    })
    
    end_time = time.time()
    duration = end_time - start_time
    
    assert response.status_code == 200
    print(f"✅ Title generation completed in {duration:.2f} seconds")
    
    if duration < 10:
        print("🚀 Performance: Excellent (< 10s)")
    elif duration < 20:
        print("👍 Performance: Good (< 20s)")
    else:
        print("⏰ Performance: Acceptable (> 20s)")

def main():
    """Run all tests"""
    print("🚀 Starting Complete Workflow Test")
    print("=" * 50)
    
    try:
        # Basic health and connectivity tests
        test_health_checks()
        
        # API key validation tests
        test_api_key_validation()
        
        # Core functionality tests
        original_data = test_title_tags_generation()
        test_generate_more_functionality(original_data)
        
        # Authentication tests
        test_authentication_endpoints()
        
        # Error handling tests
        test_error_handling()
        
        # Performance tests
        test_performance()
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED! The platform is working correctly.")
        print("\n📋 Test Summary:")
        print("   ✅ Health checks")
        print("   ✅ API key validation")
        print("   ✅ Title & tags generation")
        print("   ✅ Generate more functionality")
        print("   ✅ Authentication endpoints")
        print("   ✅ Error handling")
        print("   ✅ Performance metrics")
        
    except AssertionError as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        return False
    except Exception as e:
        print(f"\n💥 UNEXPECTED ERROR: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
