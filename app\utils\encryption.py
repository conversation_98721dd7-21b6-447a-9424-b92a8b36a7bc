from cryptography.fernet import <PERSON>rne<PERSON>
from app.config import settings
import base64
import hashlib
import logging
from typing import Optional

class EncryptionManager:
    """Handles encryption and decryption of sensitive data like API keys"""
    
    def __init__(self):
        self._fernet = None
    
    def _get_fernet(self) -> <PERSON><PERSON><PERSON>:
        """Get or create <PERSON>rnet instance"""
        if self._fernet is None:
            # Create a key from the secret key
            key = hashlib.sha256(settings.secret_key.encode()).digest()
            encoded_key = base64.urlsafe_b64encode(key)
            self._fernet = Fernet(encoded_key)
        return self._fernet
    
    def encrypt_api_key(self, api_key: str) -> str:
        """Encrypt an API key"""
        try:
            fernet = self._get_fernet()
            encrypted_key = fernet.encrypt(api_key.encode())
            return base64.urlsafe_b64encode(encrypted_key).decode()
        except Exception as e:
            logging.error(f"Error encrypting API key: {str(e)}")
            raise
    
    def decrypt_api_key(self, encrypted_key: str) -> str:
        """Decrypt an API key"""
        try:
            fernet = self._get_fernet()
            decoded_key = base64.urlsafe_b64decode(encrypted_key.encode())
            decrypted_key = fernet.decrypt(decoded_key)
            return decrypted_key.decode()
        except Exception as e:
            logging.error(f"Error decrypting API key: {str(e)}")
            raise
    
    def is_encrypted(self, value: str) -> bool:
        """Check if a value appears to be encrypted"""
        try:
            # Try to decode as base64 and check if it looks like encrypted data
            decoded = base64.urlsafe_b64decode(value.encode())
            return len(decoded) > 32  # Encrypted data should be longer than original
        except:
            return False

# Global encryption manager instance
encryption_manager = EncryptionManager()

def encrypt_user_api_key(api_key: str) -> str:
    """Encrypt user's API key for storage"""
    return encryption_manager.encrypt_api_key(api_key)

def decrypt_user_api_key(encrypted_key: str) -> str:
    """Decrypt user's API key for use"""
    return encryption_manager.decrypt_api_key(encrypted_key)

def mask_api_key(api_key: str) -> str:
    """Mask API key for display (show only first and last few characters)"""
    if len(api_key) <= 8:
        return "*" * len(api_key)
    return api_key[:4] + "*" * (len(api_key) - 8) + api_key[-4:]
