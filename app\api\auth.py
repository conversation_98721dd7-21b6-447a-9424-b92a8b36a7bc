from fastapi import APIRouter, HTTPException, Depends, <PERSON><PERSON>
from pydantic import BaseModel
from typing import Optional
import logging
import google.generativeai as genai
from app.models.user import UserResponse, ApiKeyRequest, ApiKeyValidation, UserUpdate
from app.utils.database import db_manager
from app.utils.encryption import encrypt_user_api_key, decrypt_user_api_key, mask_api_key
from app.utils.auth import (
    extract_token_from_header,
    get_current_user_from_token,
    require_authentication,
    extract_user_id_simple
)
from app.database import get_supabase_client

router = APIRouter()

async def get_current_user(authorization: Optional[str] = Header(None)) -> Optional[str]:
    """
    Extract user ID from authorization header with JWT validation
    """
    if not authorization:
        return None

    # Extract token from header
    token = extract_token_from_header(authorization)
    if not token:
        return None

    # Try JWT validation first
    try:
        user_data = await get_current_user_from_token(token)
        if user_data:
            return user_data['user_id']
    except Exception as e:
        logging.warning(f"JWT validation failed: {str(e)}")

    # Fallback to simple user ID for backward compatibility
    return extract_user_id_simple(authorization)

@router.get("/profile", response_model=UserResponse)
async def get_user_profile(current_user: Optional[str] = Depends(get_current_user)):
    """
    Get current user profile
    """
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    try:
        user = await db_manager.get_user_by_id(current_user)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return user
    except Exception as e:
        logging.error(f"Error getting user profile: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get user profile")

@router.post("/validate-api-key", response_model=ApiKeyValidation)
async def validate_gemini_api_key(request: ApiKeyRequest):
    """
    Validate user's Gemini API key
    """
    try:
        # Configure Gemini with the provided API key
        genai.configure(api_key=request.api_key)

        # Try to list models to validate the key
        models = genai.list_models()
        model_list = list(models)

        if model_list:
            return ApiKeyValidation(
                is_valid=True,
                message="API key is valid",
                key_info={
                    "masked_key": mask_api_key(request.api_key),
                    "available_models": len(model_list)
                }
            )
        else:
            return ApiKeyValidation(
                is_valid=False,
                message="API key is valid but no models available"
            )

    except Exception as e:
        logging.error(f"Error validating API key: {str(e)}")
        return ApiKeyValidation(
            is_valid=False,
            message=f"Invalid API key: {str(e)}"
        )

@router.post("/save-api-key")
async def save_user_api_key(
    request: ApiKeyRequest,
    current_user: Optional[str] = Depends(get_current_user)
):
    """
    Save user's Gemini API key (encrypted)
    """
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    try:
        # First validate the API key
        validation = await validate_gemini_api_key(request)
        if not validation.is_valid:
            raise HTTPException(status_code=400, detail=validation.message)

        # Encrypt the API key
        encrypted_key = encrypt_user_api_key(request.api_key)

        # Save to database
        update_data = UserUpdate(gemini_api_key=encrypted_key)
        updated_user = await db_manager.update_user(current_user, update_data)

        if not updated_user:
            raise HTTPException(status_code=500, detail="Failed to save API key")

        return {
            "message": "API key saved successfully",
            "masked_key": mask_api_key(request.api_key)
        }

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error saving API key: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to save API key")

@router.delete("/api-key")
async def remove_user_api_key(current_user: Optional[str] = Depends(get_current_user)):
    """
    Remove user's saved API key
    """
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    try:
        update_data = UserUpdate(gemini_api_key=None)
        updated_user = await db_manager.update_user(current_user, update_data)

        if not updated_user:
            raise HTTPException(status_code=500, detail="Failed to remove API key")

        return {"message": "API key removed successfully"}

    except Exception as e:
        logging.error(f"Error removing API key: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to remove API key")

@router.get("/usage")
async def get_user_usage(current_user: Optional[str] = Depends(get_current_user)):
    """
    Get user's current usage statistics
    """
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    try:
        user = await db_manager.get_user_by_id(current_user)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return {
            "usage_count": user.usage_count,
            "monthly_limit": user.monthly_usage_limit,
            "remaining": max(0, user.monthly_usage_limit - user.usage_count),
            "tier": user.tier
        }

    except Exception as e:
        logging.error(f"Error getting user usage: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get usage statistics")

@router.get("/verify-token")
async def verify_jwt_token(authorization: Optional[str] = Header(None)):
    """
    Verify JWT token and return user information
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")

    token = extract_token_from_header(authorization)
    if not token:
        raise HTTPException(status_code=401, detail="Invalid authorization header format")

    try:
        user_data = await require_authentication(token)
        return {
            "valid": True,
            "user_id": user_data['user_id'],
            "email": user_data.get('email'),
            "role": user_data.get('role'),
            "message": "Token is valid"
        }
    except HTTPException as e:
        return {
            "valid": False,
            "message": e.detail
        }

@router.get("/me")
async def get_authenticated_user(authorization: Optional[str] = Header(None)):
    """
    Get current authenticated user information using JWT
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")

    token = extract_token_from_header(authorization)
    if not token:
        raise HTTPException(status_code=401, detail="Invalid authorization header format")

    try:
        user_data = await require_authentication(token)

        # Get user from our database
        user = await db_manager.get_user_by_id(user_data['user_id'])
        if not user:
            # User exists in Supabase auth but not in our users table
            # This might happen with new users
            raise HTTPException(status_code=404, detail="User profile not found")

        return {
            "auth_user": user_data,
            "profile": user
        }
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error getting authenticated user: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get user information")

@router.get("/health")
async def auth_health():
    """Health check for auth service"""
    return {"status": "healthy", "service": "auth"}
