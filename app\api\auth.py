from fastapi import APIRouter, HTTPException, Depends, <PERSON><PERSON>
from pydantic import BaseModel
from typing import Optional
import logging
import google.generativeai as genai
from app.models.user import UserResponse, ApiKeyRequest, ApiKeyValidation, UserUpdate
from app.utils.mock_database import mock_db_manager as db_manager
from app.utils.encryption import encrypt_user_api_key, decrypt_user_api_key, mask_api_key
from app.database import get_supabase_client

router = APIRouter()

async def get_current_user(authorization: Optional[str] = Header(None)) -> Optional[str]:
    """
    Extract user ID from authorization header
    For now, we'll use a simple approach. In production, validate JWT token.
    """
    if not authorization:
        return None

    # For testing, we'll accept a simple user ID
    # In production, decode and validate JW<PERSON> token from Supabase
    if authorization.startswith("Bearer "):
        token = authorization[7:]
        # TODO: Validate JWT token with Supabase
        # For now, return the token as user ID for testing
        return token

    return None

@router.get("/profile", response_model=UserResponse)
async def get_user_profile(current_user: Optional[str] = Depends(get_current_user)):
    """
    Get current user profile
    """
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    try:
        user = await db_manager.get_user_by_id(current_user)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return user
    except Exception as e:
        logging.error(f"Error getting user profile: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get user profile")

@router.post("/validate-api-key", response_model=ApiKeyValidation)
async def validate_gemini_api_key(request: ApiKeyRequest):
    """
    Validate user's Gemini API key
    """
    try:
        # Configure Gemini with the provided API key
        genai.configure(api_key=request.api_key)

        # Try to list models to validate the key
        models = genai.list_models()
        model_list = list(models)

        if model_list:
            return ApiKeyValidation(
                is_valid=True,
                message="API key is valid",
                key_info={
                    "masked_key": mask_api_key(request.api_key),
                    "available_models": len(model_list)
                }
            )
        else:
            return ApiKeyValidation(
                is_valid=False,
                message="API key is valid but no models available"
            )

    except Exception as e:
        logging.error(f"Error validating API key: {str(e)}")
        return ApiKeyValidation(
            is_valid=False,
            message=f"Invalid API key: {str(e)}"
        )

@router.post("/save-api-key")
async def save_user_api_key(
    request: ApiKeyRequest,
    current_user: Optional[str] = Depends(get_current_user)
):
    """
    Save user's Gemini API key (encrypted)
    """
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    try:
        # First validate the API key
        validation = await validate_gemini_api_key(request)
        if not validation.is_valid:
            raise HTTPException(status_code=400, detail=validation.message)

        # Encrypt the API key
        encrypted_key = encrypt_user_api_key(request.api_key)

        # Save to database
        update_data = UserUpdate(gemini_api_key=encrypted_key)
        updated_user = await db_manager.update_user(current_user, update_data)

        if not updated_user:
            raise HTTPException(status_code=500, detail="Failed to save API key")

        return {
            "message": "API key saved successfully",
            "masked_key": mask_api_key(request.api_key)
        }

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error saving API key: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to save API key")

@router.delete("/api-key")
async def remove_user_api_key(current_user: Optional[str] = Depends(get_current_user)):
    """
    Remove user's saved API key
    """
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    try:
        update_data = UserUpdate(gemini_api_key=None)
        updated_user = await db_manager.update_user(current_user, update_data)

        if not updated_user:
            raise HTTPException(status_code=500, detail="Failed to remove API key")

        return {"message": "API key removed successfully"}

    except Exception as e:
        logging.error(f"Error removing API key: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to remove API key")

@router.get("/usage")
async def get_user_usage(current_user: Optional[str] = Depends(get_current_user)):
    """
    Get user's current usage statistics
    """
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    try:
        user = await db_manager.get_user_by_id(current_user)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return {
            "usage_count": user.usage_count,
            "monthly_limit": user.monthly_usage_limit,
            "remaining": max(0, user.monthly_usage_limit - user.usage_count),
            "tier": user.tier
        }

    except Exception as e:
        logging.error(f"Error getting user usage: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get usage statistics")

@router.get("/health")
async def auth_health():
    """Health check for auth service"""
    return {"status": "healthy", "service": "auth"}
