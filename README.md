# Content Creator Agents Platform

AI-powered tools for content creators, starting with YouTube optimization agents.

## Features

- **Title & Tags Agent**: Generate optimized YouTube titles and tags
- **User Authentication**: Google OAuth integration via Supabase
- **Freemium Model**: Free tier with user's API key, premium with platform API
- **Generate More**: On-demand additional content generation

## Tech Stack

- **Backend**: FastAPI
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth (Google OAuth)
- **AI**: Google Gemini API
- **Python**: 3.13+

## Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your Supabase and Gemini API credentials
   ```

3. **Run Development Server**
   ```bash
   python -m app.main
   # or
   uvicorn app.main:app --reload
   ```

4. **Access API Documentation**
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

## API Endpoints

### Agents
- `POST /api/agents/generate-titles-tags` - Generate titles and tags
- `POST /api/agents/generate-more-titles-tags` - Generate additional content

### Authentication
- `GET /api/auth/profile` - Get user profile
- `POST /api/auth/validate-api-key` - Validate Gemini API key
- `POST /api/auth/save-api-key` - Save user's API key

## Project Structure

```
app/
├── main.py              # FastAPI application
├── config.py            # Configuration settings
├── models/              # Database models
├── api/                 # API routes
│   ├── agents.py        # Agent endpoints
│   └── auth.py          # Authentication endpoints
├── agents/              # AI agent implementations
└── utils/               # Utility functions
```

## Development Status

- [x] Basic FastAPI structure
- [ ] Supabase integration
- [ ] Title & Tags agent implementation
- [ ] Google OAuth authentication
- [ ] Database models
- [ ] Generate more functionality

## Next Steps

1. Set up Supabase database and authentication
2. Implement Title & Tags agent with Gemini API
3. Add user management and API key handling
4. Build frontend interface
5. Deploy to production
