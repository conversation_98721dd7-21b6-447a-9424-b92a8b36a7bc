from supabase import create_client, Client
from app.config import settings
import logging
from typing import Optional

# Global Supabase client
supabase: Optional[Client] = None

def get_supabase_client() -> Client:
    """
    Get or create Supabase client instance
    """
    global supabase
    
    if supabase is None:
        if not settings.supabase_url or not settings.supabase_key:
            raise ValueError("Supabase URL and key must be provided in environment variables")
        
        try:
            supabase = create_client(settings.supabase_url, settings.supabase_key)
            logging.info("Supabase client initialized successfully")
        except Exception as e:
            logging.error(f"Failed to initialize Supabase client: {str(e)}")
            raise
    
    return supabase

def get_supabase_admin_client() -> Client:
    """
    Get Supabase client with service role key for admin operations
    """
    if not settings.supabase_url or not settings.supabase_service_key:
        raise ValueError("Supabase URL and service key must be provided for admin operations")
    
    try:
        admin_client = create_client(settings.supabase_url, settings.supabase_service_key)
        logging.info("Supabase admin client initialized successfully")
        return admin_client
    except Exception as e:
        logging.error(f"Failed to initialize Supabase admin client: {str(e)}")
        raise

async def test_connection() -> bool:
    """
    Test Supabase connection
    """
    try:
        client = get_supabase_client()
        # Try a simple query to test connection
        response = client.table('users').select('id').limit(1).execute()
        logging.info("Supabase connection test successful")
        return True
    except Exception as e:
        logging.error(f"Supabase connection test failed: {str(e)}")
        return False

# Database initialization
async def init_database():
    """
    Initialize database connection and create tables if needed
    """
    try:
        client = get_supabase_client()
        logging.info("Database initialized successfully")
        return True
    except Exception as e:
        logging.error(f"Database initialization failed: {str(e)}")
        return False
