#!/usr/bin/env python3
"""
Test script to debug Gemini API integration
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.gemini_service import GeminiService

def test_gemini():
    try:
        # Test with your API key
        api_key = "AIzaSyAaJTHPJgKhZkuKhc-EVrFW6-AfaCzuh_g"
        service = GeminiService(api_key)
        
        print("Testing Gemini service...")
        
        result = service.generate_titles_and_tags(
            script="This is a test script about productivity tips",
            topic="Productivity",
            keywords=["productivity", "tips"],
            target_audience="professionals"
        )
        
        print("Success!")
        print(f"Titles: {result['titles']}")
        print(f"Tags: {result['tags']}")
        print(f"SEO Score: {result.get('seo_score', 'N/A')}")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gemini()
