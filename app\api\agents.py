from fastapi import APIRouter, HTTPException, Depends, <PERSON><PERSON>
from pydantic import BaseModel
from typing import List, Optional
import logging
from app.services.gemini_service import GeminiService
from app.utils.database import db_manager
from app.config import settings

router = APIRouter()

# Pydantic models for request/response
class TitleTagsRequest(BaseModel):
    script: str
    topic: str
    keywords: List[str]
    target_audience: str
    user_api_key: Optional[str] = None  # For free tier users

class TitleTagsResponse(BaseModel):
    titles: List[str]
    tags: List[str]
    seo_score: Optional[int] = None

class GenerateMoreRequest(BaseModel):
    original_request: TitleTagsRequest
    exclude_titles: List[str] = []
    exclude_tags: List[str] = []

@router.post("/generate-titles-tags", response_model=TitleTagsResponse)
async def generate_titles_tags(
    request: TitleTagsRequest,
    authorization: Optional[str] = Header(None)
):
    """
    Generate optimized titles and tags for YouTube content
    """
    try:
        # Determine which API key to use
        api_key = None
        user_id = None

        if authorization and authorization.startswith("Bearer "):
            # Extract user ID from token (simplified for now)
            user_id = authorization.replace("Bearer ", "")

            # Get user from database to check if they have an API key
            user = await db_manager.get_user_by_id(user_id)
            if user and user.gemini_api_key:
                # Free user with their own API key
                from app.utils.encryption import decrypt_user_api_key
                api_key = decrypt_user_api_key(user.gemini_api_key)
            elif user and user.tier in ["premium", "enterprise"]:
                # Premium user - use platform API key
                api_key = settings.gemini_api_key
            else:
                # Free user without API key
                if request.user_api_key:
                    api_key = request.user_api_key
                else:
                    raise HTTPException(
                        status_code=400,
                        detail="API key required. Please provide your Gemini API key or upgrade to premium."
                    )
        else:
            # No authorization - require API key in request
            if request.user_api_key:
                api_key = request.user_api_key
            else:
                raise HTTPException(
                    status_code=400,
                    detail="Authentication required or provide your Gemini API key"
                )

        # Initialize Gemini service with the appropriate API key
        gemini_service = GeminiService(api_key)

        # Generate titles and tags
        result = gemini_service.generate_titles_and_tags(
            script=request.script,
            topic=request.topic,
            keywords=request.keywords,
            target_audience=request.target_audience
        )

        # Track usage if user is authenticated
        if user_id and user:
            await db_manager.track_usage(
                user_id=user_id,
                content_type="title_tags",
                api_key_used="user" if user.gemini_api_key else "platform"
            )

        return TitleTagsResponse(
            titles=result['titles'],
            tags=result['tags'],
            seo_score=result.get('seo_score', 75)
        )

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error generating titles and tags: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to generate content")

@router.post("/generate-more-titles-tags", response_model=TitleTagsResponse)
async def generate_more_titles_tags(
    request: GenerateMoreRequest,
    authorization: Optional[str] = Header(None)
):
    """
    Generate additional titles and tags based on previous request
    """
    try:
        # Determine which API key to use (same logic as above)
        api_key = None
        user_id = None

        if authorization and authorization.startswith("Bearer "):
            user_id = authorization.replace("Bearer ", "")
            user = await db_manager.get_user_by_id(user_id)

            if user and user.gemini_api_key:
                from app.utils.encryption import decrypt_user_api_key
                api_key = decrypt_user_api_key(user.gemini_api_key)
            elif user and user.tier in ["premium", "enterprise"]:
                api_key = settings.gemini_api_key
            else:
                if request.original_request.user_api_key:
                    api_key = request.original_request.user_api_key
                else:
                    raise HTTPException(
                        status_code=400,
                        detail="API key required. Please provide your Gemini API key or upgrade to premium."
                    )
        else:
            if request.original_request.user_api_key:
                api_key = request.original_request.user_api_key
            else:
                raise HTTPException(
                    status_code=400,
                    detail="Authentication required or provide your Gemini API key"
                )

        # Initialize Gemini service
        gemini_service = GeminiService(api_key)

        # Generate more titles and tags
        result = gemini_service.generate_more_titles_and_tags(
            original_script=request.original_request.script,
            original_topic=request.original_request.topic,
            original_keywords=request.original_request.keywords,
            target_audience=request.original_request.target_audience,
            exclude_titles=request.exclude_titles,
            exclude_tags=request.exclude_tags
        )

        # Track usage if user is authenticated
        if user_id and user:
            await db_manager.track_usage(
                user_id=user_id,
                content_type="title_tags",
                api_key_used="user" if user.gemini_api_key else "platform"
            )

        return TitleTagsResponse(
            titles=result['titles'],
            tags=result['tags'],
            seo_score=result.get('seo_score', 75)
        )

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error generating more content: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to generate additional content")

@router.get("/health")
async def agents_health():
    """Health check for agents service"""
    return {"status": "healthy", "service": "agents"}
