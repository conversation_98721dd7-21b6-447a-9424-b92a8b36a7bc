#!/usr/bin/env python3
"""
List available Gemini models
"""
import google.generativeai as genai

def list_models():
    try:
        api_key = "AIzaSyAaJTHPJgKhZkuKhc-EVrFW6-AfaCzuh_g"
        genai.configure(api_key=api_key)
        
        print("Available Gemini models:")
        for model in genai.list_models():
            if 'generateContent' in model.supported_generation_methods:
                print(f"- {model.name}")
                
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    list_models()
