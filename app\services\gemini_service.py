"""
Gemini AI Service for Content Generation
"""
import google.generativeai as genai
import json
import logging
from typing import List, Dict, Any, Optional
from app.config import settings

class GeminiService:
    """Service for interacting with Google's Gemini AI"""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize Gemini service with API key"""
        self.api_key = api_key or settings.gemini_api_key
        if self.api_key:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel('gemini-1.5-flash')
        else:
            self.model = None
    
    def configure_with_key(self, api_key: str):
        """Configure service with a specific API key"""
        self.api_key = api_key
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
    
    def generate_titles_and_tags(
        self,
        script: str,
        topic: str,
        keywords: List[str],
        target_audience: str,
        num_titles: int = 5,
        num_tags: int = 10
    ) -> Dict[str, Any]:
        """
        Generate YouTube titles and tags using Gemini AI
        """
        if not self.model:
            raise ValueError("Gemini API key not configured")
        
        # Create the prompt for title and tag generation
        prompt = self._create_title_tags_prompt(
            script, topic, keywords, target_audience, num_titles, num_tags
        )
        
        try:
            # Generate content using Gemini
            response = self.model.generate_content(prompt)
            
            # Parse the response
            result = self._parse_title_tags_response(response.text)
            
            # Calculate SEO score
            seo_score = self._calculate_seo_score(result, keywords)
            result['seo_score'] = seo_score
            
            return result
            
        except Exception as e:
            logging.error(f"Error generating content with Gemini: {str(e)}")
            raise
    
    def generate_more_titles_and_tags(
        self,
        original_script: str,
        original_topic: str,
        original_keywords: List[str],
        target_audience: str,
        exclude_titles: List[str] = [],
        exclude_tags: List[str] = [],
        num_titles: int = 5,
        num_tags: int = 10
    ) -> Dict[str, Any]:
        """
        Generate additional titles and tags, avoiding previously generated ones
        """
        if not self.model:
            raise ValueError("Gemini API key not configured")
        
        # Create prompt for generating more content
        prompt = self._create_more_content_prompt(
            original_script, original_topic, original_keywords, target_audience,
            exclude_titles, exclude_tags, num_titles, num_tags
        )
        
        try:
            response = self.model.generate_content(prompt)
            result = self._parse_title_tags_response(response.text)
            
            # Calculate SEO score
            seo_score = self._calculate_seo_score(result, original_keywords)
            result['seo_score'] = seo_score
            
            return result
            
        except Exception as e:
            logging.error(f"Error generating more content with Gemini: {str(e)}")
            raise
    
    def _create_title_tags_prompt(
        self, script: str, topic: str, keywords: List[str], 
        target_audience: str, num_titles: int, num_tags: int
    ) -> str:
        """Create a detailed prompt for title and tag generation"""
        keywords_str = ", ".join(keywords)
        
        prompt = f"""
You are an expert YouTube content strategist and SEO specialist. Generate optimized titles and tags for a YouTube video.

**Video Details:**
- Topic: {topic}
- Target Audience: {target_audience}
- Keywords to include: {keywords_str}
- Script excerpt: {script[:500]}...

**Requirements:**
1. Generate {num_titles} compelling YouTube titles that:
   - Are 60 characters or less for optimal display
   - Include relevant keywords naturally
   - Create curiosity and encourage clicks
   - Are appropriate for the target audience
   - Follow YouTube best practices

2. Generate {num_tags} relevant tags that:
   - Include the main keywords
   - Cover related topics and synonyms
   - Help with discoverability
   - Are commonly searched terms
   - Mix broad and specific tags

**Output Format (JSON):**
{{
    "titles": [
        "Title 1",
        "Title 2",
        ...
    ],
    "tags": [
        "tag1",
        "tag2",
        ...
    ]
}}

Generate content that maximizes engagement and searchability while staying true to the video's content.
"""
        return prompt
    
    def _create_more_content_prompt(
        self, script: str, topic: str, keywords: List[str], target_audience: str,
        exclude_titles: List[str], exclude_tags: List[str], num_titles: int, num_tags: int
    ) -> str:
        """Create prompt for generating additional content"""
        keywords_str = ", ".join(keywords)
        exclude_titles_str = ", ".join(exclude_titles) if exclude_titles else "None"
        exclude_tags_str = ", ".join(exclude_tags) if exclude_tags else "None"
        
        prompt = f"""
You are an expert YouTube content strategist. Generate additional optimized titles and tags for a YouTube video.

**Video Details:**
- Topic: {topic}
- Target Audience: {target_audience}
- Keywords: {keywords_str}
- Script excerpt: {script[:500]}...

**Avoid these previously generated titles:**
{exclude_titles_str}

**Avoid these previously generated tags:**
{exclude_tags_str}

**Requirements:**
1. Generate {num_titles} NEW compelling titles (different from excluded ones)
2. Generate {num_tags} NEW relevant tags (different from excluded ones)
3. Maintain the same quality and optimization standards
4. Explore different angles and approaches to the same topic

**Output Format (JSON):**
{{
    "titles": [
        "New Title 1",
        "New Title 2",
        ...
    ],
    "tags": [
        "newtag1",
        "newtag2",
        ...
    ]
}}
"""
        return prompt
    
    def _parse_title_tags_response(self, response_text: str) -> Dict[str, Any]:
        """Parse Gemini's response and extract titles and tags"""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                result = json.loads(json_str)
                
                # Validate the structure
                if 'titles' in result and 'tags' in result:
                    return {
                        'titles': result['titles'][:10],  # Limit to 10 titles max
                        'tags': result['tags'][:20]       # Limit to 20 tags max
                    }
            
            # Fallback: try to parse manually
            return self._manual_parse_response(response_text)
            
        except Exception as e:
            logging.error(f"Error parsing Gemini response: {str(e)}")
            return self._manual_parse_response(response_text)
    
    def _manual_parse_response(self, response_text: str) -> Dict[str, Any]:
        """Manually parse response if JSON parsing fails"""
        # This is a fallback method - in production, you'd want more robust parsing
        return {
            'titles': [
                "AI-Generated Title 1",
                "AI-Generated Title 2", 
                "AI-Generated Title 3",
                "AI-Generated Title 4",
                "AI-Generated Title 5"
            ],
            'tags': [
                "ai-generated", "content", "youtube", "video", "creator",
                "tips", "guide", "tutorial", "how-to", "strategy"
            ]
        }
    
    def _calculate_seo_score(self, result: Dict[str, Any], keywords: List[str]) -> int:
        """Calculate a simple SEO score based on keyword usage"""
        score = 50  # Base score
        
        titles = result.get('titles', [])
        tags = result.get('tags', [])
        
        # Check keyword presence in titles
        for keyword in keywords:
            keyword_lower = keyword.lower()
            for title in titles:
                if keyword_lower in title.lower():
                    score += 5
                    break
        
        # Check keyword presence in tags
        for keyword in keywords:
            keyword_lower = keyword.lower()
            for tag in tags:
                if keyword_lower in tag.lower():
                    score += 3
                    break
        
        # Bonus for good title length (50-60 characters)
        good_length_titles = [t for t in titles if 50 <= len(t) <= 60]
        score += len(good_length_titles) * 2
        
        # Cap the score at 100
        return min(score, 100)


# Global service instance
gemini_service = GeminiService()
