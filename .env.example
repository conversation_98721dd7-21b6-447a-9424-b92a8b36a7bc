# Supabase Configuration
SUPABASE_URL=https://vqrpbbluoakjtgvfdrmu.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZxcnBiYmx1b2FranRndmZkcm11Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwOTAzOTIsImV4cCI6MjA2NjY2NjM5Mn0.mWCEEYZhAHN_lCEQ0lofWXJUPl7CydseYYwbbw4lPxs
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZxcnBiYmx1b2FranRndmZkcm11Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA5MDM5MiwiZXhwIjoyMDY2NjY2MzkyfQ.4WVU26i_iVIIcF369Atu0M2s9zGglydHkd9MYcXPF18

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/dbname

# Gemini API (for premium users)
GEMINI_API_KEY=AIzaSyAaJTHPJgKhZkuKhc-EVrFW6-AfaCzuh_g

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production

# Development
DEBUG=True
