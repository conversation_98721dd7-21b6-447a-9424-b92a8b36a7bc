from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class ContentType(str, Enum):
    TITLE_TAGS = "title_tags"
    SCRIPT = "script"
    DESCRIPTION = "description"
    IDEA = "idea"

class GenerationStatus(str, Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"

class GeneratedContent(BaseModel):
    """Model for storing generated content"""
    id: str
    user_id: str
    content_type: ContentType
    input_data: Dict[str, Any]  # Original request data
    output_data: Dict[str, Any]  # Generated content
    status: GenerationStatus = GenerationStatus.PENDING
    error_message: Optional[str] = None
    generation_time_ms: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class TitleTagsInput(BaseModel):
    """Input model for title and tags generation"""
    script: str
    topic: str
    keywords: List[str]
    target_audience: str
    additional_context: Optional[str] = None

class TitleTagsOutput(BaseModel):
    """Output model for title and tags generation"""
    titles: List[str]
    tags: List[str]
    seo_score: Optional[int] = None
    optimization_tips: Optional[List[str]] = None

class GenerateRequest(BaseModel):
    """Base model for generation requests"""
    content_type: ContentType
    input_data: Dict[str, Any]
    user_api_key: Optional[str] = None  # For free tier users

class GenerateResponse(BaseModel):
    """Base model for generation responses"""
    id: str
    content_type: ContentType
    output_data: Dict[str, Any]
    status: GenerationStatus
    generation_time_ms: Optional[int] = None
    created_at: datetime

class GenerateMoreRequest(BaseModel):
    """Model for generating additional content"""
    original_content_id: str
    exclude_items: List[str] = []  # Items to exclude from new generation
    additional_context: Optional[str] = None

class ContentHistory(BaseModel):
    """Model for user's content generation history"""
    id: str
    content_type: ContentType
    preview: str  # Short preview of the content
    created_at: datetime
    status: GenerationStatus

class ContentHistoryResponse(BaseModel):
    """Response model for content history"""
    items: List[ContentHistory]
    total_count: int
    page: int
    page_size: int
