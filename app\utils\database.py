from app.database import get_supabase_client, get_supabase_admin_client
from app.models.user import User, UserCreate, UserUpdate, UserResponse
from app.models.content import GeneratedContent, ContentType, GenerationStatus
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime
import json

class DatabaseManager:
    """Database operations manager"""

    def __init__(self):
        self.client = None
        self.admin_client = None

    def _get_client(self):
        """Get Supabase client, initialize if needed"""
        if self.client is None:
            self.client = get_supabase_client()
        return self.client

    def _get_admin_client(self):
        """Get Supabase admin client, initialize if needed"""
        if self.admin_client is None:
            self.admin_client = get_supabase_admin_client()
        return self.admin_client
    
    # User operations
    async def get_user_by_id(self, user_id: str) -> Optional[UserResponse]:
        """Get user by ID"""
        try:
            client = self._get_client()
            response = client.table('users').select('*').eq('id', user_id).single().execute()
            if response.data:
                user_data = response.data
                return UserResponse(
                    **user_data,
                    has_api_key=bool(user_data.get('gemini_api_key'))
                )
            return None
        except Exception as e:
            logging.error(f"Error getting user by ID {user_id}: {str(e)}")
            return None
    
    async def get_user_by_email(self, email: str) -> Optional[UserResponse]:
        """Get user by email"""
        try:
            response = self.client.table('users').select('*').eq('email', email).single().execute()
            if response.data:
                user_data = response.data
                return UserResponse(
                    **user_data,
                    has_api_key=bool(user_data.get('gemini_api_key'))
                )
            return None
        except Exception as e:
            logging.error(f"Error getting user by email {email}: {str(e)}")
            return None
    
    async def create_user(self, user_data: UserCreate) -> Optional[UserResponse]:
        """Create new user"""
        try:
            response = self.client.table('users').insert(user_data.model_dump()).execute()
            if response.data:
                user_data = response.data[0]
                return UserResponse(
                    **user_data,
                    has_api_key=bool(user_data.get('gemini_api_key'))
                )
            return None
        except Exception as e:
            logging.error(f"Error creating user: {str(e)}")
            return None
    
    async def update_user(self, user_id: str, update_data: UserUpdate) -> Optional[UserResponse]:
        """Update user information"""
        try:
            # Filter out None values
            update_dict = {k: v for k, v in update_data.model_dump().items() if v is not None}
            if not update_dict:
                return await self.get_user_by_id(user_id)
            
            response = self.client.table('users').update(update_dict).eq('id', user_id).execute()
            if response.data:
                user_data = response.data[0]
                return UserResponse(
                    **user_data,
                    has_api_key=bool(user_data.get('gemini_api_key'))
                )
            return None
        except Exception as e:
            logging.error(f"Error updating user {user_id}: {str(e)}")
            return None
    
    async def increment_usage(self, user_id: str) -> bool:
        """Increment user's usage count"""
        try:
            response = self.client.rpc('increment_usage', {'user_id': user_id}).execute()
            return True
        except Exception as e:
            logging.error(f"Error incrementing usage for user {user_id}: {str(e)}")
            # Fallback to manual increment
            try:
                user = await self.get_user_by_id(user_id)
                if user:
                    await self.update_user(user_id, UserUpdate(usage_count=user.usage_count + 1))
                    return True
            except Exception as fallback_error:
                logging.error(f"Fallback increment also failed: {str(fallback_error)}")
            return False
    
    # Content operations
    async def create_generated_content(
        self, 
        user_id: str, 
        content_type: ContentType, 
        input_data: Dict[str, Any],
        output_data: Optional[Dict[str, Any]] = None,
        status: GenerationStatus = GenerationStatus.PENDING
    ) -> Optional[str]:
        """Create new generated content record"""
        try:
            content_data = {
                'user_id': user_id,
                'content_type': content_type.value,
                'input_data': input_data,
                'output_data': output_data,
                'status': status.value
            }
            
            response = self.client.table('generated_content').insert(content_data).execute()
            if response.data:
                return response.data[0]['id']
            return None
        except Exception as e:
            logging.error(f"Error creating generated content: {str(e)}")
            return None
    
    async def update_generated_content(
        self, 
        content_id: str, 
        output_data: Dict[str, Any], 
        status: GenerationStatus,
        generation_time_ms: Optional[int] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """Update generated content with results"""
        try:
            update_data = {
                'output_data': output_data,
                'status': status.value,
                'updated_at': datetime.utcnow().isoformat()
            }
            
            if generation_time_ms is not None:
                update_data['generation_time_ms'] = generation_time_ms
            
            if error_message is not None:
                update_data['error_message'] = error_message
            
            response = self.client.table('generated_content').update(update_data).eq('id', content_id).execute()
            return len(response.data) > 0
        except Exception as e:
            logging.error(f"Error updating generated content {content_id}: {str(e)}")
            return False
    
    async def get_generated_content(self, content_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get generated content by ID"""
        try:
            response = self.client.table('generated_content').select('*').eq('id', content_id).eq('user_id', user_id).single().execute()
            return response.data if response.data else None
        except Exception as e:
            logging.error(f"Error getting generated content {content_id}: {str(e)}")
            return None
    
    async def get_user_content_history(
        self, 
        user_id: str, 
        content_type: Optional[ContentType] = None,
        limit: int = 20,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get user's content generation history"""
        try:
            query = self.client.table('generated_content').select('*').eq('user_id', user_id)
            
            if content_type:
                query = query.eq('content_type', content_type.value)
            
            response = query.order('created_at', desc=True).range(offset, offset + limit - 1).execute()
            return response.data if response.data else []
        except Exception as e:
            logging.error(f"Error getting content history for user {user_id}: {str(e)}")
            return []
    
    # Usage logging
    async def log_usage(
        self, 
        user_id: str, 
        content_type: ContentType, 
        api_key_used: str,
        tokens_used: int = 0,
        cost_usd: float = 0.0
    ) -> bool:
        """Log API usage"""
        try:
            usage_data = {
                'user_id': user_id,
                'content_type': content_type.value,
                'api_key_used': api_key_used,
                'tokens_used': tokens_used,
                'cost_usd': cost_usd
            }
            
            response = self.client.table('usage_logs').insert(usage_data).execute()
            return len(response.data) > 0
        except Exception as e:
            logging.error(f"Error logging usage: {str(e)}")
            return False

# Global database manager instance
db_manager = DatabaseManager()
