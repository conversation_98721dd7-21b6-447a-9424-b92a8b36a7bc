from pydantic import BaseModel, EmailStr
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum

class UserTier(str, Enum):
    FREE = "free"
    PREMIUM = "premium"
    ENTERPRISE = "enterprise"

class User(BaseModel):
    """User model for database operations"""
    id: str
    email: EmailStr
    name: str
    avatar_url: Optional[str] = None
    tier: UserTier = UserTier.FREE
    gemini_api_key: Optional[str] = None  # Encrypted for free users
    usage_count: int = 0
    monthly_usage_limit: int = 100  # Free tier limit
    created_at: datetime
    updated_at: datetime
    is_active: bool = True
    
    class Config:
        from_attributes = True

class UserCreate(BaseModel):
    """Model for creating new users"""
    email: EmailStr
    name: str
    avatar_url: Optional[str] = None
    tier: UserTier = UserTier.FREE

class UserUpdate(BaseModel):
    """Model for updating user information"""
    name: Optional[str] = None
    avatar_url: Optional[str] = None
    tier: Optional[UserTier] = None
    gemini_api_key: Optional[str] = None
    monthly_usage_limit: Optional[int] = None
    is_active: Optional[bool] = None

class UserResponse(BaseModel):
    """Model for user API responses"""
    id: str
    email: str
    name: str
    avatar_url: Optional[str] = None
    tier: UserTier
    usage_count: int
    monthly_usage_limit: int
    has_api_key: bool  # Don't expose the actual key
    created_at: datetime
    is_active: bool

class ApiKeyRequest(BaseModel):
    """Model for API key requests"""
    api_key: str

class ApiKeyValidation(BaseModel):
    """Model for API key validation response"""
    is_valid: bool
    message: str
    key_info: Optional[Dict[str, Any]] = None
